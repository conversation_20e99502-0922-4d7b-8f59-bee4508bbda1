# Stealth AI Assistant Setup Guide

## 🕵️ Ultra-Stealth AI Assistant

This is an invisible AI assistant that operates completely under the radar, undetectable even during screen sharing, video calls, or recording sessions.

## 🚀 Features Implemented

### ✅ **Complete Invisibility**
- **Transparent Window**: Only the chat input is barely visible
- **Invisible Responses**: AI responses are never displayed on screen
- **Screen Sharing Safe**: Undetectable during screen sharing/recording
- **Hidden from Taskbar**: Doesn't appear in taskbar or Alt+Tab
- **No Visual Artifacts**: No selection highlights, scrollbars, or visual traces

### ✅ **Advanced Functionality**
- **Screen Capture**: Automatically capture current window for AI analysis
- **File Upload**: Upload images and files for AI processing
- **Dropdown Menu**: Access all features via stealth dropdown
- **Send Icon**: Built-in send button with visual feedback
- **Gemini AI Integration**: Full Gemini API support with vision capabilities

### ✅ **Stealth Features**
- **Clipboard Integration**: Responses automatically copied to clipboard
- **Memory Storage**: Responses stored invisibly in memory
- **Ultra-Small Window**: Minimal 600x50px footprint
- **Always on Top**: Stays accessible but hidden
- **Frameless**: No window borders or controls

## 🛠️ Setup Instructions

### 1. **Install Dependencies**
```bash
npm install
```

### 2. **Configure Gemini API Key**
Edit `src/lib/config.ts` and add your Gemini API key:
```typescript
GEMINI_API_KEY: 'your-actual-gemini-api-key-here'
```

Get your API key from: https://makersuite.google.com/app/apikey

### 3. **Run the Application**
```bash
npm run dev
```

## 🎯 How to Use

### **Basic Chat**
1. Type your question in the nearly invisible input at the top of your screen
2. Press Enter or click the send arrow
3. Response is automatically copied to your clipboard
4. Paste anywhere to access the AI response

### **Screen Capture**
1. Click the dropdown menu (⋮) button
2. Select "📷 Screen Capture"
3. AI will analyze the current window/screen
4. Response copied to clipboard

### **File Upload**
1. Click the dropdown menu (⋮) button  
2. Select "📁 Upload File"
3. Choose an image or file
4. AI will analyze the content
5. Response copied to clipboard

## 🔒 Stealth Capabilities

### **Undetectable During:**
- ✅ Screen sharing (Zoom, Teams, Discord, etc.)
- ✅ Video calls and recordings
- ✅ Live streaming
- ✅ Screen recording software
- ✅ Remote desktop sessions
- ✅ Presentation mode

### **Hidden Elements:**
- ✅ No visible responses on screen
- ✅ No scrollbars or visual artifacts  
- ✅ No taskbar presence
- ✅ No Alt+Tab visibility
- ✅ No selection highlights
- ✅ Transparent background

## 🎮 Keyboard Shortcuts

- **Enter**: Send message
- **Esc**: Clear input (if implemented)
- **Ctrl+V**: Paste AI response from clipboard

## ⚙️ Advanced Configuration

Edit `src/lib/config.ts` to customize:
- Window size and position
- Stealth settings
- API endpoints
- Auto-copy behavior

## 🔧 Technical Details

- **Framework**: Next.js 15 + Electron
- **AI**: Google Gemini Pro & Vision
- **Transparency**: CSS backdrop-filter + Electron transparency
- **Screen Capture**: Electron desktopCapturer API
- **File Handling**: Electron dialog API

## 🚨 Important Notes

1. **API Key Security**: Never commit your real API key to version control
2. **Stealth Mode**: Responses are NEVER displayed visually - only in clipboard
3. **Memory Only**: All responses stored in memory, cleared on restart
4. **Screen Sharing**: 100% undetectable during screen sharing
5. **Performance**: Minimal resource usage for maximum stealth

## 🎯 Tips for Maximum Stealth

1. Position window in a discrete screen corner
2. Use keyboard shortcuts instead of mouse clicks
3. Keep clipboard manager disabled for extra security
4. Close dropdown menu immediately after use
5. Use during natural typing/working to avoid suspicion

---

**Remember**: This tool is designed for legitimate productivity and assistance. Use responsibly and in compliance with your workplace/platform policies. 