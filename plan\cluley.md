
here is how cluely ai works,

Cluely AI is a desktop application that acts as a real-time, AI-powered assistant or "copilot." It's designed to provide users with instant information and answers during a variety of online interactions, such as video meetings, sales calls, online exams, and job interviews. The company has gained significant attention for its provocative marketing, which openly suggests using the tool to "cheat on everything."



Here's a breakdown of its core functionality:

1. Screen and Audio Monitoring:
<PERSON>luely runs in the background on a user's computer and has the ability to "see" everything on the screen and "hear" all the audio from the microphone. This allows it to understand the context of the user's current activity in real-time.


2. Invisible Overlay:
The key to <PERSON>luely's operation is its transparent, on-screen overlay. This window is only visible to the user and is designed to be undetectable to others in a video conference, even when the user is sharing their screen. This allows the user to read the AI-generated information without anyone else knowing.

3. Real-Time, Context-Aware Responses:
By combining the information from the screen and audio, <PERSON><PERSON>ly provides what it calls "context-aware" assistance. For example:

In a meeting or sales call: It can provide real-time talking points, answers to questions, and rebuttals to objections.

During an online exam or interview: It can display answers to questions that appear on the screen.

4. Integration with AI Models:
<PERSON>luely uses powerful AI language models, such as Google's Gemini or OpenAI's ChatGPT, to generate its responses. Its main innovation lies in its ability to effectively "stitch" together the context from the user's screen and audio and feed it to these models to get the most relevant answers.

5. Document Analysis:
Users can upload documents, such as study guides, sales playbooks, or technical manuals. Cluely can then scan these documents and provide relevant information during a call or exam.


6. Post-Interaction Features:
After a meeting or call, Cluely can generate summaries and even draft personalized follow-up emails based on the conversation.