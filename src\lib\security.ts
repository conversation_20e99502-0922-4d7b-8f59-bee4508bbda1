/**
 * Simple Security for Interview Assistant
 * Basic input validation and sanitization
 */

export interface ValidationResult {
  valid: boolean;
  reason?: string;
}

export class SimpleSecurityValidator {
  // Basic input validation
  validateInput(input: string): ValidationResult {
    if (!input || input.trim().length === 0) {
      return { valid: false, reason: 'Input cannot be empty' };
    }

    if (input.length > 5000) {
      return { valid: false, reason: 'Input too long' };
    }

    // Basic XSS prevention
    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(input)) {
        return { valid: false, reason: 'Invalid characters detected' };
      }
    }

    return { valid: true };
  }

  // Basic input sanitization
  sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }
}

export const securityValidator = new SimpleSecurityValidator(); 