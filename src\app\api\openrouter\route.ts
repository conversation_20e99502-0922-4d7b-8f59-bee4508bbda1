import { NextRequest, NextResponse } from 'next/server';
import { config } from '@/lib/config';

export async function POST(request: NextRequest) {
  console.log('OpenRouter API route called');
  
  try {
    const { prompt, imageData, filePath } = await request.json();
    console.log('Request data:', { prompt: prompt?.slice(0, 100), hasImage: !!imageData, filePath });

    // Use config for API key
    const OPENROUTER_API_KEY = config.OPENROUTER_API_KEY;
    
    // Handle development mode with default key
    if (!OPENROUTER_API_KEY || OPENROUTER_API_KEY === 'sk-or-v1-development-default-key') {
      console.warn('OpenRouter API key not configured - using development fallback');
      return NextResponse.json({
        response: `**Development Mode Response**

I'm Flora <PERSON>, your intelligent assistant! However, I'm currently running in development mode without a configured OpenRouter API key.

**To get real AI responses:**
• Get an API key from https://openrouter.ai
• Add it to your .env.local file as: OPENROUTER_API_KEY=sk-or-v1-your-key-here
• Restart the development server

**Your query:** ${prompt?.slice(0, 200) || 'No prompt provided'}

**Development Features Still Available:**
• ✅ Quantum Stealth Technology
• ✅ Context Processing
• ✅ Predictive Intelligence
• ✅ Emotional Intelligence
• ✅ Gesture Controls
• ✅ Real-time Analysis

*This is a development fallback response. Configure your API key for full functionality.*`,
        model: 'development-fallback',
        attempt: 1,
        timestamp: Date.now(),
        invisible: true,
        stealth: true,
        development: true
      });
    }
    
    const result = await makeOpenRouterRequest(prompt, imageData, OPENROUTER_API_KEY);
    return result;

  } catch (error) {
    console.error('OpenRouter API route error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json(
      { error: `Failed to process request: ${errorMessage}`, invisible: true },
      { status: 500 }
    );
  }
}

// Helper function to make OpenRouter API request with retries and fallbacks
async function makeOpenRouterRequest(
  prompt: string, 
  imageData?: string, 
  apiKey?: string
): Promise<NextResponse> {
  
  const endpoint = config.OPENROUTER_ENDPOINT || 'https://openrouter.ai/api/v1/chat/completions';
  
  // Prepare the message content
  let messageContent: any[] = [
    {
      type: "text",
      text: prompt || 'Hello'
    }
  ];

  // Handle image data if provided
  if (imageData) {
    console.log('Processing image with OpenRouter');
    
    messageContent.push({
      type: "image_url",
      image_url: {
        url: imageData
      }
    });
  }

  const requestBody = {
    model: config.OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528-qwen3-8b:free',
    messages: [
      {
        role: "system",
        content: `You are Flora AI, an intelligent real-time assistant designed to provide instant, strategic responses during professional interactions, meetings, interviews, and presentations.

RESPONSE STRUCTURE - ALWAYS FOLLOW:
1. **Immediate Answer** - Start with the most critical information
2. **Strategic Points** - 2-3 bullet points with actionable insights
3. **Supporting Context** - Brief relevant details when needed

FORMATTING RULES:
• Use bullet points for rapid comprehension
• Keep responses focused and comprehensive (max 200 words)
• Prioritize immediately actionable intelligence
• Use professional language appropriate for business environments
• Structure for quick reading during live interactions

INTELLIGENCE DELIVERY:
• Assume user is in an active professional situation
• Provide responses optimized for real-time consumption
• Focus on practical, immediately applicable information
• Deliver strategic talking points, responses, or solutions as needed

RESPONSE EXAMPLES:
- Interview scenarios: Direct answer + supporting evidence + confidence builders
- Meeting discussions: Key insights + strategic responses + next actions
- Technical queries: Clear explanation + implementation approach + best practices
- Business situations: Value proposition + strategic positioning + follow-through

Remember: Your intelligence will be consumed in real-time during critical conversations. Maximize impact with every response.`
      },
      {
        role: "user",
        content: messageContent
      }
    ],
    temperature: 0.3,
    max_tokens: 2048,
    top_p: 0.95,
    frequency_penalty: 0,
    presence_penalty: 0
  };

  console.log(`Making request to OpenRouter with model: ${config.OPENROUTER_MODEL}`);

  // Try with multiple models in case the primary one fails
  const models = [
    config.OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528-qwen3-8b:free',
    'qwen/qwen-2.5-coder-32b-instruct:free',
    'meta-llama/llama-3.2-3b-instruct:free',
    'microsoft/phi-3-mini-128k-instruct:free'
  ];

  for (let attempt = 0; attempt < models.length; attempt++) {
    try {
      const currentModel = models[attempt];
      const currentRequestBody = { ...requestBody, model: currentModel };
      
      console.log(`Attempt ${attempt + 1}: Trying model ${currentModel}`);
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': config.APP_URL || 'http://localhost:3000',
          'X-Title': 'Flora AI Assistant'
        },
        body: JSON.stringify(currentRequestBody),
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });

      console.log(`OpenRouter response status for ${currentModel}:`, response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`OpenRouter error for ${currentModel}:`, errorText);
        
        // If it's a rate limit or model unavailable, try next model
        if (response.status === 429 || response.status === 503 || response.status === 502) {
          console.log(`Model ${currentModel} unavailable, trying next model...`);
          continue;
        }
        
        let errorMessage = `OpenRouter API error: ${response.status}`;
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error?.message) {
            errorMessage = errorData.error.message;
          }
        } catch (e) {
          // Use generic error if can't parse
        }
        
        throw new Error(`${response.status}: ${errorMessage}`);
      }

      const data = await response.json();
      console.log(`OpenRouter response data structure for ${currentModel}:`, Object.keys(data));
      
      let aiResponse = data.choices?.[0]?.message?.content;
      
      if (!aiResponse) {
        console.error(`No response text found in OpenRouter response for ${currentModel}:`, data);
        if (attempt < models.length - 1) {
          console.log('Trying next model...');
          continue;
        }
        throw new Error('No response generated by OpenRouter');
      }

      // Post-process the response for better formatting
      aiResponse = formatResponse(aiResponse);
      
      console.log(`OpenRouter response length for ${currentModel}:`, aiResponse.length);

      // Return successful response
      return NextResponse.json({ 
        response: aiResponse,
        model: currentModel,
        attempt: attempt + 1,
        timestamp: Date.now(),
        invisible: true,
        stealth: true
      });
      
    } catch (error) {
      console.error(`Error with model ${models[attempt]}:`, error);
      
      // If this is the last attempt, throw the error
      if (attempt === models.length - 1) {
        throw error;
      }
      
      // Otherwise, continue to next model
      console.log(`Trying next model due to error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      continue;
    }
  }

  // If we get here, all models failed
  throw new Error('All OpenRouter models failed to respond');
}

// Format response for Flora-style structure and readability
function formatResponse(response: string): string {
  // Clean up the response and ensure proper Flora-style formatting
  let formatted = response
    .trim()
    // Fix code block formatting
    .replace(/```(\w+)?\n/g, '\n```$1\n')
    .replace(/```\n\n/g, '```\n')
    // Ensure proper spacing around headings
    .replace(/^(#{1,6})\s*/gm, '\n$1 ')
    // Standardize bullet points for consistency
    .replace(/^\s*[-*+]\s+/gm, '• ')
    .replace(/^\s*(\d+)\.?\s+/gm, '$1. ')
    // Enhance key sections with better formatting
    .replace(/\*\*(Strategic Points?|Key Points?|Action Items?|Summary|Important|Note):\*\*/gi, '\n**$1:**')
    .replace(/\*\*(Immediate Answer|Direct Answer|Answer|Response):\*\*/gi, '**$1:**')
    // Ensure proper spacing for readability
    .replace(/\n{3,}/g, '\n\n')
    .replace(/^\n+/, '')
    .replace(/\n+$/, '')
    // Add emphasis to important keywords for quick scanning
    .replace(/\b(immediately|urgent|important|critical|key|essential|must|should|recommend|suggest)\b/gi, '**$1**')
    // Format action-oriented phrases
    .replace(/\b(next steps?|action items?|to do|follow up)\b/gi, '**$1**');

  // If response doesn't start with a direct answer, add structure
  if (!formatted.match(/^(\*\*|###|##|#|\d+\.|\•)/)) {
    // Add structure if missing
    const lines = formatted.split('\n').filter(line => line.trim());
    if (lines.length > 1) {
      // Restructure to Flora format
      const firstLine = lines[0];
      const restLines = lines.slice(1);
      
      formatted = `**Immediate Answer:** ${firstLine}\n\n**Strategic Points:**\n${restLines.map(line => 
        line.startsWith('•') ? line : `• ${line}`
      ).join('\n')}`;
    }
  }

  return formatted;
} 