/**
 * Simple AI System for Interview Assistance
 * Basic OpenRouter API integration for interview Q&A
 */

interface AIResponse {
  response: string;
  model: string;
  timestamp: number;
}

export class SimpleAISystem {
  async processQuery(query: string): Promise<AIResponse> {
    try {
      const response = await fetch('/api/openrouter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: query
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        response: data.response || 'I apologize, but I could not process your request.',
        model: data.model || 'unknown',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('AI processing error:', error);
      return {
        response: 'I apologize, but I encountered an error. Please try again.',
        model: 'error',
        timestamp: Date.now()
      };
    }
  }
}

export const aiEnsemble = new SimpleAISystem(); 