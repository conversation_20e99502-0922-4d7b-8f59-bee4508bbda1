const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Window control methods
  moveWindow: (deltaX, deltaY) => ipcRenderer.invoke('move-window', deltaX, deltaY),
  getWindowPosition: () => ipcRenderer.invoke('get-window-position'),
  setWindowPosition: (x, y) => ipcRenderer.invoke('set-window-position', x, y),
  
  // Screen capture methods
  getScreenSources: () => ipcRenderer.invoke('get-screen-sources'),
  
  // File dialog methods
  showFileDialog: () => ipcRenderer.invoke('show-file-dialog'),
  
  // Utility methods
  isElectron: true,
});

// Also expose a simpler electron object for backwards compatibility
contextBridge.exposeInMainWorld('electron', {
  invoke: (channel, ...args) => {
    const validChannels = [
      'move-window',
      'get-window-position', 
      'set-window-position',
      'get-screen-sources',
      'show-file-dialog'
    ];
    
    if (validChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, ...args);
    }
    throw new Error(`Invalid IPC channel: ${channel}`);
  }
}); 