@import "tailwindcss/preflight";
@import "tailwindcss/utilities";

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  background: transparent !important;
  background-image: none !important;
  background-pattern: none !important;
  color: rgba(255, 255, 255, 0.9);
  /* Prevent desktop background extension during drag */
  -webkit-app-region: no-drag;
  app-region: no-drag;
  /* Ensure complete transparency */
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}

/* Window container optimizations */
.window-container {
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  /* Prevent visual artifacts during drag */
  image-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
}

/* Optimized dragging styles */
.window-container.dragging {
  pointer-events: auto !important;
  will-change: transform, opacity;
  /* Smooth hardware acceleration during drag */
  transform: translateZ(0) scale(1);
  -webkit-transform: translateZ(0) scale(1);
  /* Prevent background bleed */
  isolation: isolate;
  contain: layout style paint;
}

/* Drag handle optimizations */
.drag-handle {
  will-change: opacity, transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Prevent interference with background */
  isolation: isolate;
  contain: strict;
}

.drag-handle:hover {
  opacity: 1 !important;
}

.drag-handle:active {
  transform: translateZ(0) scale(0.98);
  -webkit-transform: translateZ(0) scale(0.98);
}

/* Glass effect optimizations - Dark black glassy design */
.glass-input {
  will-change: backdrop-filter, background-color;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Dark glassy styling */
  isolation: isolate;
  position: relative;
}

.glass-input:focus {
  backdrop-filter: blur(30px) saturate(1.3) !important;
  -webkit-backdrop-filter: blur(30px) saturate(1.3) !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(15, 15, 15, 0.95) 50%, rgba(0, 0, 0, 0.98) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.08), 0 6px 24px rgba(0, 0, 0, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.12) !important;
  color: #ffffff !important;
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
  opacity: 1 !important;
  font-weight: 400 !important;
}

/* Response display optimizations - Enhanced visibility */
.stealth-response-display {
  will-change: opacity, transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Enhanced visibility properties */
  contain: strict;
  isolation: isolate;
}

/* Prevent selection during drag */
.dragging * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: none !important;
}

.dragging .drag-handle {
  pointer-events: auto !important;
}

/* Scrollbar styling for response area */
.stealth-response-display::-webkit-scrollbar {
  width: 10px;
}

.stealth-response-display::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.stealth-response-display::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  border: 2px solid rgba(0, 0, 0, 0.2);
}

.stealth-response-display::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Anti-screen-capture optimizations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Prevent screen sharing detection */
  image-rendering: auto;
  image-rendering: crisp-edges;
}

/* Enhanced visibility for user but invisible to screen capture */
input, button, .drag-handle {
  /* These elements remain functional and visible to user */
  pointer-events: auto !important;
  cursor: pointer !important;
  /* Anti-detection properties */
  -webkit-font-feature-settings: normal;
  font-feature-settings: normal;
}

.drag-handle {
  cursor: grab !important;
}

.dragging .drag-handle {
  cursor: grabbing !important;
}

/* Prevent text selection artifacts during drag */
::selection {
  background: rgba(255, 255, 255, 0.2);
}

::-moz-selection {
  background: rgba(255, 255, 255, 0.2);
}
