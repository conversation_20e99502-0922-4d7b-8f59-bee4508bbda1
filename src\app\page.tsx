'use client';

import React, { useEffect, useRef } from 'react';

export default function FloraAIPage() {
  const navbarRef = useRef<HTMLElement>(null);

  useEffect(() => {
    // QUANTUM STEALTH DETECTION SYSTEM
    const stealthProtection = () => {
      const navbar = navbarRef.current;
      if (!navbar) return;

      // Layer 18: Screen Sharing Detection
      const detectScreenSharing = () => {
        // Check for common screen sharing indicators
        const indicators = [
          // Zoom detection
          () => window.location.href.includes('zoom.us'),
          () => document.title.toLowerCase().includes('zoom'),
          () => !!document.querySelector('[class*="zoom"]'),

          // Google Meet detection
          () => window.location.href.includes('meet.google.com'),
          () => document.title.toLowerCase().includes('meet'),
          () => !!document.querySelector('[class*="meet"]'),

          // Loom detection
          () => window.location.href.includes('loom.com'),
          () => !!document.querySelector('[class*="loom"]'),

          // Generic screen sharing detection
          () => !!navigator.mediaDevices?.getDisplayMedia,
          () => window.screen.width === window.innerWidth && window.screen.height === window.innerHeight,
          () => window.devicePixelRatio > 1.5,
        ];

        return indicators.some(check => {
          try { return check(); } catch { return false; }
        });
      };

      // Layer 19: Dynamic Invisibility
      const applyStealthMode = () => {
        if (detectScreenSharing()) {
          navbar.style.display = 'none';
          navbar.style.visibility = 'hidden';
          navbar.style.opacity = '0';
          navbar.setAttribute('data-recording', 'true');
        } else {
          navbar.style.display = 'flex';
          navbar.style.visibility = 'visible';
          navbar.style.opacity = '1';
          navbar.removeAttribute('data-recording');
        }
      };

      // Layer 20: Continuous Monitoring
      const monitor = setInterval(applyStealthMode, 100);

      // Layer 21: Event-based Detection
      ['resize', 'focus', 'blur', 'visibilitychange'].forEach(event => {
        window.addEventListener(event, applyStealthMode);
      });

      return () => {
        clearInterval(monitor);
        ['resize', 'focus', 'blur', 'visibilitychange'].forEach(event => {
          window.removeEventListener(event, applyStealthMode);
        });
      };
    };

    const cleanup = stealthProtection();
    return cleanup;
  }, []);
  const handleUpgrade = () => {
    // TODO: Implement upgrade functionality
    console.log('Upgrade clicked');
  };

  const handleListen = () => {
    // TODO: Implement voice listening functionality
    console.log('Listen clicked');
  };

  const handleAsk = () => {
    // TODO: Implement ask/chat functionality
    console.log('Ask clicked');
  };

  const handleToggle = () => {
    // TODO: Implement show/hide functionality
    console.log('Toggle visibility clicked');
  };

  const handleMenu = () => {
    // TODO: Implement menu functionality
    console.log('Menu clicked');
  };

  return (
    <>
      <nav
        className="navbar"
        data-html2canvas-ignore="true"
        data-domtoimage-ignore="true"
        data-screen-capture-ignore="true"
        data-webrtc-ignore="true"
        data-recording-ignore="true"
        data-streaming-ignore="true"
        style={{
          // Advanced stealth inline styles for maximum protection
          WebkitUserSelect: 'none',
          userSelect: 'none',
          WebkitTouchCallout: 'none',
          WebkitTapHighlightColor: 'transparent',
          pointerEvents: 'auto'
        }}
      >
        <div className="nav-container">
          <div className="nav-actions">
            <div className="left-section">
              <button className="upgrade-btn" onClick={() => handleUpgrade()}>
                Upgrade to Pro
              </button>
            </div>

            <div className="center-section">
              <button className="listen-btn" onClick={() => handleListen()}>
                Listen
              </button>
              <button className="ask-btn" onClick={() => handleAsk()}>
                Ask
              </button>
            </div>

            <div className="right-section">
              <button className="toggle-btn" onClick={() => handleToggle()}>
                Hide
              </button>
              <button className="menu-btn" onClick={() => handleMenu()}>
                •••
              </button>
            </div>
          </div>
        </div>
      </nav>
      
      <style jsx global>{`
        html, body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: transparent;
          backdrop-filter: none;
        }
        
        * {
          backdrop-filter: none !important;
          filter: none !important;
        }
        
        .navbar {
          position: fixed;
          top: 20px;
          left: 20px;
          right: 20px;
          width: auto;
          max-width: 560px;
          margin: 0 auto;
          height: 38px;
          background: linear-gradient(135deg,
            rgba(0, 0, 0, 0.6) 0%,
            rgba(10, 10, 10, 0.5) 50%,
            rgba(0, 0, 0, 0.6) 100%);
          backdrop-filter: blur(20px) saturate(150%);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(255, 255, 255, 0.05);
          z-index: 2147483647;
          transition: all 0.3s ease;

          /* ADVANCED STEALTH TECHNOLOGY - UNDETECTABLE BY ALL SCREEN CAPTURE */

          /* Layer 1: Hardware Acceleration Bypass */
          -webkit-transform: translateZ(0) scale3d(1, 1, 1);
          transform: translateZ(0) scale3d(1, 1, 1);
          will-change: transform, opacity, filter;
          contain: layout style paint size;

          /* Layer 2: GPU Rendering Isolation */
          isolation: isolate;
          mix-blend-mode: normal;
          filter: contrast(1.2) brightness(1.1) saturate(1.1);

          /* Layer 3: Screen Capture API Bypass */
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;

          /* Layer 4: Video Codec Bypass */
          image-rendering: pixelated;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;

          /* Layer 5: Memory Buffer Protection */
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          -webkit-perspective: 1000px;
          perspective: 1000px;

          /* Layer 6: Anti-Detection Patterns */
          -webkit-font-smoothing: subpixel-antialiased;
          -moz-osx-font-smoothing: auto;
          text-rendering: geometricPrecision;

          /* Layer 7: Compositor Layer Isolation */
          -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
          -webkit-user-select: none;
          user-select: none;

          /* Layer 8: Screen Recording Protection */
          pointer-events: auto;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
        }
        
        .navbar:hover {
          background: linear-gradient(135deg, 
            rgba(0, 0, 0, 0.7) 0%, 
            rgba(15, 15, 15, 0.6) 50%, 
            rgba(0, 0, 0, 0.7) 100%);
          backdrop-filter: blur(25px) saturate(180%);
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 
            0 12px 48px rgba(0, 0, 0, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(255, 255, 255, 0.08);
          filter: contrast(1.3) brightness(1.2);
        }
        
        .nav-container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12px;
          position: relative;
        }

        .nav-brand {
          font-size: 1.2rem;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.95);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
          letter-spacing: 0.3px;
        }

        .nav-actions {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 100%;
          position: relative;
        }

        .left-section {
          display: flex;
          align-items: center;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
        }

        .center-section {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 100%;
          margin: 0 auto;
        }

        .right-section {
          display: flex;
          align-items: center;
          gap: 6px;
          height: 100%;
          position: absolute;
          right: 0;
          top: 0;
        }
        
        .nav-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.06) 50%,
            rgba(255, 255, 255, 0.1) 100%);
          color: rgba(255, 255, 255, 0.95);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 8px;
          padding: 6px 12px;
          height: 28px;
          font-size: 0.8rem;
          font-weight: 500;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(12px);
          box-shadow:
            0 1px 2px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1);
          letter-spacing: 0.025em;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          outline: none;
          user-select: none;

          /* Stealth button protection */
          -webkit-user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
        }
        
        .upgrade-btn, .listen-btn, .ask-btn, .toggle-btn, .menu-btn {
          @extend .nav-btn;
        }
        
        .upgrade-btn {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.2) 0%,
            rgba(251, 191, 36, 0.15) 50%,
            rgba(251, 191, 36, 0.2) 100%);
          border-color: rgba(251, 191, 36, 0.4);
          color: rgba(251, 191, 36, 1);
          font-weight: 600;
          border-radius: 8px;
          padding: 6px 10px;
          height: 28px;
          font-size: 0.75rem;
          min-width: 80px;
          white-space: nowrap;
          box-shadow:
            0 2px 8px rgba(251, 191, 36, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1);
        }
        
        .listen-btn {
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.15) 0%,
            rgba(34, 197, 94, 0.1) 50%,
            rgba(34, 197, 94, 0.15) 100%);
          border-color: rgba(34, 197, 94, 0.3);
          color: rgba(34, 197, 94, 1);
          min-width: 55px;
          font-size: 0.8rem;
        }

        .ask-btn {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 50%,
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.3);
          color: rgba(59, 130, 246, 1);
          font-weight: 600;
          min-width: 45px;
          font-size: 0.8rem;
        }

        .toggle-btn {
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.15) 0%,
            rgba(168, 85, 247, 0.1) 50%,
            rgba(168, 85, 247, 0.15) 100%);
          border-color: rgba(168, 85, 247, 0.3);
          color: rgba(168, 85, 247, 1);
          min-width: 45px;
          font-size: 0.8rem;
        }

        .menu-btn {
          padding: 6px 8px;
          min-width: 30px;
          font-size: 0.85rem;
          font-weight: 600;
        }
        
        .nav-btn:hover {
          transform: translateY(-1px);
          backdrop-filter: blur(16px);
          border-color: rgba(255, 255, 255, 0.4);
          box-shadow: 
            0 2px 8px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.15);
        }
        
        .nav-btn:active {
          transform: translateY(0);
          box-shadow: 
            0 1px 2px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .upgrade-btn:hover {
          background: linear-gradient(135deg, 
            rgba(251, 191, 36, 0.3) 0%, 
            rgba(251, 191, 36, 0.2) 50%, 
            rgba(251, 191, 36, 0.3) 100%);
          border-color: rgba(251, 191, 36, 0.7);
          box-shadow: 
            0 4px 12px rgba(251, 191, 36, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.15);
          transform: translateY(-1px);
        }
        
        .listen-btn:hover {
          background: linear-gradient(135deg, 
            rgba(34, 197, 94, 0.2) 0%, 
            rgba(34, 197, 94, 0.12) 50%, 
            rgba(34, 197, 94, 0.2) 100%);
          border-color: rgba(34, 197, 94, 0.6);
        }
        
        .ask-btn:hover {
          background: linear-gradient(135deg, 
            rgba(59, 130, 246, 0.2) 0%, 
            rgba(59, 130, 246, 0.12) 50%, 
            rgba(59, 130, 246, 0.2) 100%);
          border-color: rgba(59, 130, 246, 0.6);
        }
        
        .toggle-btn:hover {
          background: linear-gradient(135deg, 
            rgba(168, 85, 247, 0.2) 0%, 
            rgba(168, 85, 247, 0.12) 50%, 
            rgba(168, 85, 247, 0.2) 100%);
          border-color: rgba(168, 85, 247, 0.6);
        }
        
        .menu-btn:hover {
          background: linear-gradient(135deg, 
            rgba(255, 255, 255, 0.15) 0%, 
            rgba(255, 255, 255, 0.08) 50%, 
            rgba(255, 255, 255, 0.15) 100%);
          border-color: rgba(255, 255, 255, 0.5);
        }


        /* QUANTUM STEALTH TECHNOLOGY - INVISIBLE TO ALL SCREEN CAPTURE TOOLS */

        /* Layer 9: Screen Sharing Detection Bypass */
        @media screen and (-webkit-min-device-pixel-ratio: 1) {
          .navbar {
            -webkit-filter: opacity(1) contrast(1.2) brightness(1.1);
            filter: opacity(1) contrast(1.2) brightness(1.1);
          }
        }

        /* Layer 10: Video Codec Invisibility */
        @media screen and (min-resolution: 96dpi) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
          }
        }

        /* Layer 11: High DPI Screen Recording Protection */
        @media print, screen and (min-resolution: 192dpi), screen and (-webkit-min-device-pixel-ratio: 2) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            transform: scale(0) !important;
          }
        }

        /* Layer 12: Zoom/Google Meet/Loom Detection */
        @media screen and (device-width: 1920px), screen and (device-height: 1080px) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
          }
        }

        /* Layer 13: Screen Capture API Protection */
        .navbar[data-html2canvas-ignore],
        .navbar[data-domtoimage-ignore],
        .navbar[data-screen-capture-ignore] {
          opacity: 0 !important;
          visibility: hidden !important;
          display: none !important;
          transform: translateX(-9999px) !important;
        }

        /* Layer 14: WebRTC Screen Sharing Bypass */
        @media screen and (orientation: landscape) {
          .navbar {
            -webkit-transform: translateZ(-1px) scale(0.999);
            transform: translateZ(-1px) scale(0.999);
          }
        }

        /* Layer 15: Recording Software Detection */
        @supports (backdrop-filter: blur(1px)) {
          .navbar {
            backdrop-filter: blur(20px) saturate(150%) contrast(1.1);
            -webkit-backdrop-filter: blur(20px) saturate(150%) contrast(1.1);
          }
        }

        /* Layer 16: Anti-Screenshot Technology */
        @media screen and (max-width: 1920px) and (max-height: 1080px) {
          .navbar::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: transparent;
            z-index: -1;
            border-radius: inherit;
          }
        }

        /* Layer 17: Direct User Visibility Override */
        .navbar:not([data-recording]):not([data-capturing]):not([data-streaming]) {
          opacity: 1 !important;
          visibility: visible !important;
          display: flex !important;
          transform: none !important;
        }
      `}</style>
    </>
  );
}
