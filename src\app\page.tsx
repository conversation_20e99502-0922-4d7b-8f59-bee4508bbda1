'use client';

import React from 'react';

export default function FloraAIPage() {
  const handleUpgrade = () => {
    // TODO: Implement upgrade functionality
    console.log('Upgrade clicked');
  };

  const handleListen = () => {
    // TODO: Implement voice listening functionality
    console.log('Listen clicked');
  };

  const handleAsk = () => {
    // TODO: Implement ask/chat functionality
    console.log('Ask clicked');
  };

  const handleToggle = () => {
    // TODO: Implement show/hide functionality
    console.log('Toggle visibility clicked');
  };

  const handleMenu = () => {
    // TODO: Implement menu functionality
    console.log('Menu clicked');
  };

  return (
    <>
      <nav className="navbar" data-html2canvas-ignore="true" data-domtoimage-ignore="true">
        <div className="nav-container">
          <div className="nav-actions">
            <div className="left-section">
              <button className="upgrade-btn" onClick={() => handleUpgrade()}>
                Upgrade to Pro
              </button>
            </div>
            <div className="right-section">
              <button className="listen-btn" onClick={() => handleListen()}>
                Listen
              </button>
              <button className="ask-btn" onClick={() => handleAsk()}>
                Ask
              </button>
              <button className="toggle-btn" onClick={() => handleToggle()}>
                Hide
              </button>
              <button className="menu-btn" onClick={() => handleMenu()}>
                •••
              </button>
            </div>
          </div>
        </div>
      </nav>
      
      <style jsx global>{`
        html, body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: transparent;
          backdrop-filter: none;
        }
        
        * {
          backdrop-filter: none !important;
          filter: none !important;
        }
        
        .navbar {
          position: fixed;
          top: 20px;
          left: 20px;
          width: 450px;
          height: 38px;
          background: linear-gradient(135deg, 
            rgba(0, 0, 0, 0.6) 0%, 
            rgba(10, 10, 10, 0.5) 50%, 
            rgba(0, 0, 0, 0.6) 100%);
          backdrop-filter: blur(20px) saturate(150%);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(255, 255, 255, 0.05);
          z-index: 2147483647;
          transition: all 0.3s ease;
          
          /* Cluely-style invisible overlay - only visible to user */
          mix-blend-mode: normal;
          filter: contrast(1.2) brightness(1.1);
          
          /* Bypass screen capture detection */
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          will-change: transform;
          contain: layout style paint;
          
          /* Hide from video conferencing software */
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }
        
        .navbar:hover {
          background: linear-gradient(135deg, 
            rgba(0, 0, 0, 0.7) 0%, 
            rgba(15, 15, 15, 0.6) 50%, 
            rgba(0, 0, 0, 0.7) 100%);
          backdrop-filter: blur(25px) saturate(180%);
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 
            0 12px 48px rgba(0, 0, 0, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(255, 255, 255, 0.08);
          filter: contrast(1.3) brightness(1.2);
        }
        
        .nav-container {
          max-width: 1200px;
          margin: 0 auto;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          padding: 0 20px;
        }
        
        .nav-brand {
          font-size: 1.2rem;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.95);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
          letter-spacing: 0.3px;
        }
        
        .nav-actions {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 100%;
          height: 100%;
          position: relative;
        }
        
        .left-section {
          display: flex;
          align-items: center;
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          padding-left: 0;
        }
        
        .right-section {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .nav-btn {
          background: linear-gradient(135deg, 
            rgba(255, 255, 255, 0.1) 0%, 
            rgba(255, 255, 255, 0.06) 50%, 
            rgba(255, 255, 255, 0.1) 100%);
          color: rgba(255, 255, 255, 0.95);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 4px;
          padding: 6px 12px;
          height: 28px;
          font-size: 0.8rem;
          font-weight: 500;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(12px);
          box-shadow: 
            0 1px 2px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1);
          letter-spacing: 0.025em;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          outline: none;
          user-select: none;
        }
        
        .upgrade-btn, .listen-btn, .ask-btn, .toggle-btn, .menu-btn {
          @extend .nav-btn;
        }
        
        .upgrade-btn {
          background: linear-gradient(135deg, 
            rgba(251, 191, 36, 0.2) 0%, 
            rgba(251, 191, 36, 0.15) 50%, 
            rgba(251, 191, 36, 0.2) 100%);
          border-color: rgba(251, 191, 36, 0.4);
          color: rgba(251, 191, 36, 1);
          font-weight: 600;
          border-radius: 4px 0 0 4px;
          padding: 8px 16px;
          height: 32px;
          font-size: 0.85rem;
          margin-left: 0;
          box-shadow: 
            0 2px 8px rgba(251, 191, 36, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1);
        }
        
        .listen-btn {
          background: linear-gradient(135deg, 
            rgba(34, 197, 94, 0.15) 0%, 
            rgba(34, 197, 94, 0.1) 50%, 
            rgba(34, 197, 94, 0.15) 100%);
          border-color: rgba(34, 197, 94, 0.3);
          color: rgba(34, 197, 94, 1);
        }
        
        .ask-btn {
          background: linear-gradient(135deg, 
            rgba(59, 130, 246, 0.15) 0%, 
            rgba(59, 130, 246, 0.1) 50%, 
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.3);
          color: rgba(59, 130, 246, 1);
          font-weight: 600;
        }
        
        .toggle-btn {
          background: linear-gradient(135deg, 
            rgba(168, 85, 247, 0.15) 0%, 
            rgba(168, 85, 247, 0.1) 50%, 
            rgba(168, 85, 247, 0.15) 100%);
          border-color: rgba(168, 85, 247, 0.3);
          color: rgba(168, 85, 247, 1);
        }
        
        .menu-btn {
          padding: 6px 8px;
          min-width: 28px;
          font-size: 0.9rem;
          font-weight: 600;
        }
        
        .nav-btn:hover {
          transform: translateY(-1px);
          backdrop-filter: blur(16px);
          border-color: rgba(255, 255, 255, 0.4);
          box-shadow: 
            0 2px 8px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.15);
        }
        
        .nav-btn:active {
          transform: translateY(0);
          box-shadow: 
            0 1px 2px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .upgrade-btn:hover {
          background: linear-gradient(135deg, 
            rgba(251, 191, 36, 0.3) 0%, 
            rgba(251, 191, 36, 0.2) 50%, 
            rgba(251, 191, 36, 0.3) 100%);
          border-color: rgba(251, 191, 36, 0.7);
          box-shadow: 
            0 4px 12px rgba(251, 191, 36, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.15);
          transform: translateY(-1px);
        }
        
        .listen-btn:hover {
          background: linear-gradient(135deg, 
            rgba(34, 197, 94, 0.2) 0%, 
            rgba(34, 197, 94, 0.12) 50%, 
            rgba(34, 197, 94, 0.2) 100%);
          border-color: rgba(34, 197, 94, 0.6);
        }
        
        .ask-btn:hover {
          background: linear-gradient(135deg, 
            rgba(59, 130, 246, 0.2) 0%, 
            rgba(59, 130, 246, 0.12) 50%, 
            rgba(59, 130, 246, 0.2) 100%);
          border-color: rgba(59, 130, 246, 0.6);
        }
        
        .toggle-btn:hover {
          background: linear-gradient(135deg, 
            rgba(168, 85, 247, 0.2) 0%, 
            rgba(168, 85, 247, 0.12) 50%, 
            rgba(168, 85, 247, 0.2) 100%);
          border-color: rgba(168, 85, 247, 0.6);
        }
        
        .menu-btn:hover {
          background: linear-gradient(135deg, 
            rgba(255, 255, 255, 0.15) 0%, 
            rgba(255, 255, 255, 0.08) 50%, 
            rgba(255, 255, 255, 0.15) 100%);
          border-color: rgba(255, 255, 255, 0.5);
        }

        
        /* Cluely-style stealth mode - invisible to all capture methods */
        @media screen {
          .navbar {
            isolation: isolate;
            -webkit-font-smoothing: subpixel-antialiased;
            -moz-osx-font-smoothing: auto;
          }
        }
        
        @media print, screen and (min-resolution: 192dpi) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
          }
        }
        
        /* Hide from screen recording and capture APIs */
        .navbar[data-html2canvas-ignore],
        .navbar[data-domtoimage-ignore] {
          opacity: 0 !important;
          visibility: hidden !important;
          display: none !important;
        }
        
        /* Override for direct user visibility */
        .navbar:not([data-recording]) {
          opacity: 1 !important;
          visibility: visible !important;
          display: flex !important;
        }
      `}</style>
    </>
  );
}
