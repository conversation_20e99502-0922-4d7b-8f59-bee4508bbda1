'use client';

import React, { useEffect, useRef } from 'react';

export default function FloraAIPage() {
  const navbarRef = useRef<HTMLElement>(null);
  const [isChatOpen, setIsChatOpen] = React.useState(false);
  const [chatInput, setChatInput] = React.useState('');
  const [chatMessages, setChatMessages] = React.useState<Array<{role: 'user' | 'assistant', content: string}>>([]);
  const [isLoading, setIsLoading] = React.useState(false);

  useEffect(() => {
    // QUANTUM STEALTH DETECTION SYSTEM
    const stealthProtection = () => {
      const navbar = navbarRef.current;
      if (!navbar) return;

      // Layer 18: Screen Sharing Detection
      const detectScreenSharing = () => {
        // Check for common screen sharing indicators
        const indicators = [
          // Zoom detection
          () => window.location.href.includes('zoom.us'),
          () => document.title.toLowerCase().includes('zoom'),
          () => !!document.querySelector('[class*="zoom"]'),

          // Google Meet detection
          () => window.location.href.includes('meet.google.com'),
          () => document.title.toLowerCase().includes('meet'),
          () => !!document.querySelector('[class*="meet"]'),

          // Loom detection
          () => window.location.href.includes('loom.com'),
          () => !!document.querySelector('[class*="loom"]'),

          // Generic screen sharing detection
          () => !!navigator.mediaDevices?.getDisplayMedia,
          () => window.screen.width === window.innerWidth && window.screen.height === window.innerHeight,
          () => window.devicePixelRatio > 1.5,
        ];

        return indicators.some(check => {
          try { return check(); } catch { return false; }
        });
      };

      // Layer 19: Dynamic Invisibility - DEVELOPMENT MODE
      const applyStealthMode = () => {
        // Always visible in development mode
        navbar.style.display = 'flex';
        navbar.style.visibility = 'visible';
        navbar.style.opacity = '1';
        navbar.removeAttribute('data-recording');

        // Production stealth mode (commented out for development):
        /*
        if (detectScreenSharing()) {
          navbar.style.display = 'none';
          navbar.style.visibility = 'hidden';
          navbar.style.opacity = '0';
          navbar.setAttribute('data-recording', 'true');
        }
        */
      };

      // Layer 20: Continuous Monitoring
      const monitor = setInterval(applyStealthMode, 100);

      // Layer 21: Event-based Detection
      ['resize', 'focus', 'blur', 'visibilitychange'].forEach(event => {
        window.addEventListener(event, applyStealthMode);
      });

      return () => {
        clearInterval(monitor);
        ['resize', 'focus', 'blur', 'visibilitychange'].forEach(event => {
          window.removeEventListener(event, applyStealthMode);
        });
      };
    };

    const cleanup = stealthProtection();
    return cleanup;
  }, []);

  // Keyboard shortcut: Ctrl+Shift+Enter to open chat
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'Enter') {
        e.preventDefault();
        setIsChatOpen(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleUpgrade = () => {
    window.open('https://floraai.pro/upgrade', '_blank');
  };

  const handleAsk = () => {
    setIsChatOpen(!isChatOpen);
  };



  const sendMessage = async () => {
    if (!chatInput.trim() || isLoading) return;

    const userMessage = chatInput.trim();
    setChatInput('');
    setIsLoading(true);

    // Add user message to chat
    setChatMessages(prev => [...prev, { role: 'user', content: userMessage }]);

    try {
      const response = await fetch('/api/openrouter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: userMessage
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();

      // Add AI response to chat
      setChatMessages(prev => [...prev, {
        role: 'assistant',
        content: data.response || 'I apologize, but I could not process your request.'
      }]);
    } catch (error) {
      console.error('Chat error:', error);
      setChatMessages(prev => [...prev, {
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try again.'
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <>
      <nav
        ref={navbarRef}
        className="navbar"
        data-html2canvas-ignore="true"
        data-domtoimage-ignore="true"
        data-screen-capture-ignore="true"
        data-webrtc-ignore="true"
        data-recording-ignore="true"
        data-streaming-ignore="true"
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          right: '20px',
          width: 'auto',
          maxWidth: '540px',
          margin: '0 auto',
          height: '40px',
          background: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(30px) saturate(200%)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          zIndex: 999999,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 20px',
          visibility: 'visible',
          opacity: 1,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
        }}
      >
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center', margin: '0 auto' }}>
          <button className="ask-btn" onClick={() => handleAsk()}>
            Ask
          </button>
        </div>

        <div style={{ position: 'absolute', right: '0', display: 'flex', alignItems: 'center' }}>
          <button className="upgrade-btn" onClick={() => handleUpgrade()}>
            Upgrade to Pro
          </button>
        </div>
      </nav>

      {/* Professional Chat Interface */}
      {isChatOpen && (
        <div
          className="chat-container"
          style={{
            background: 'transparent',
            border: 'none',
            boxShadow: 'none',
            height: '320px'
          }}
        >
          <div className="chat-input-container" style={{ position: 'relative', padding: '12px' }}>
            <div style={{ position: 'relative', width: '100%' }}>
              <textarea
                className="chat-input"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Ask Flora AI anything..."
                rows={1}
                disabled={isLoading}
                style={{
                  width: '100%',
                  background: 'transparent',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'rgba(255, 255, 255, 0.95)',
                  paddingRight: '50px',
                  paddingLeft: '12px',
                  paddingTop: '10px',
                  paddingBottom: '10px',
                  height: '42px',
                  borderRadius: '8px',
                  fontSize: '0.9rem',
                  resize: 'none',
                  outline: 'none',
                  fontFamily: 'inherit'
                }}
              />
              <button
                className="chat-send"
                onClick={sendMessage}
                disabled={!chatInput.trim() || isLoading}
                style={{
                  position: 'absolute',
                  right: '6px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: !chatInput.trim() || isLoading
                    ? 'transparent'
                    : 'rgba(59, 130, 246, 0.2)',
                  border: 'none',
                  color: !chatInput.trim() || isLoading
                    ? 'rgba(255, 255, 255, 0.3)'
                    : 'rgba(59, 130, 246, 0.9)',
                  cursor: !chatInput.trim() || isLoading ? 'not-allowed' : 'pointer',
                  padding: '8px',
                  borderRadius: '6px',
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'all 0.2s ease'
                }}
              >
                ➤
              </button>
            </div>
            <button
              className="chat-close"
              onClick={() => setIsChatOpen(false)}
              style={{
                position: 'absolute',
                right: '8px',
                top: '-30px',
                background: 'transparent',
                border: 'none',
                color: 'rgba(255, 255, 255, 0.6)',
                cursor: 'pointer',
                fontSize: '18px',
                padding: '4px',
                borderRadius: '4px',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
            >×</button>
          </div>

          <div className="chat-messages">
            {chatMessages.length === 0 ? (
              <div
                className="chat-welcome"
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  justifyContent: 'center',
                  paddingTop: '15px',
                  height: 'auto',
                  minHeight: '60px'
                }}
              >
                {/* Flora Logo - Black and White */}
                <img
                  src="/flora.png"
                  alt="Flora AI"
                  style={{
                    width: '60px',
                    height: '60px',
                    objectFit: 'contain',
                    opacity: 0.3,
                    filter: 'grayscale(100%) contrast(1.2)',
                    marginBottom: '0'
                  }}
                />
              </div>
            ) : (
              chatMessages.map((message, index) => (
                <div key={index} className={`chat-message ${message.role}`}>
                  <div className="message-content">
                    {message.content}
                  </div>
                </div>
              ))
            )}
            {isLoading && (
              <div className="chat-message assistant">
                <div className="message-content loading">
                  <span className="typing-indicator">●●●</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <style jsx global>{`
        html, body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: transparent;
          backdrop-filter: none;
        }
        
        * {
          backdrop-filter: none !important;
          filter: none !important;
        }
        
        .navbar {
          /* Styles are now inline for guaranteed visibility */
        }

        .upgrade-btn {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.1) 0%,
            rgba(251, 191, 36, 0.06) 50%,
            rgba(251, 191, 36, 0.1) 100%);
          border: 1px solid rgba(251, 191, 36, 0.2);
          color: rgba(251, 191, 36, 0.95);
          font-weight: 500;
          border-radius: 6px;
          padding: 4px 12px;
          height: 26px;
          font-size: 0.75rem;
          min-width: 90px;
          white-space: nowrap;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .listen-btn, .ask-btn, .toggle-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.08) 0%,
            rgba(255, 255, 255, 0.04) 50%,
            rgba(255, 255, 255, 0.08) 100%);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.95);
          font-weight: 500;
          border-radius: 6px;
          padding: 4px 10px;
          height: 26px;
          font-size: 0.75rem;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .listen-btn {
          min-width: 55px;
          color: rgba(34, 197, 94, 0.95);
          border-color: rgba(34, 197, 94, 0.2);
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.1) 0%,
            rgba(34, 197, 94, 0.06) 50%,
            rgba(34, 197, 94, 0.1) 100%);
        }

        .ask-btn {
          min-width: 45px;
          color: rgba(59, 130, 246, 0.95);
          border-color: rgba(59, 130, 246, 0.2);
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.1) 0%,
            rgba(59, 130, 246, 0.06) 50%,
            rgba(59, 130, 246, 0.1) 100%);
        }

        .toggle-btn {
          min-width: 45px;
          color: rgba(168, 85, 247, 0.95);
          border-color: rgba(168, 85, 247, 0.2);
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.1) 0%,
            rgba(168, 85, 247, 0.06) 50%,
            rgba(168, 85, 247, 0.1) 100%);
        }

        .menu-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.06) 0%,
            rgba(255, 255, 255, 0.03) 50%,
            rgba(255, 255, 255, 0.06) 100%);
          border: 1px solid rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.9);
          padding: 4px 8px;
          min-width: 30px;
          font-size: 0.7rem;
          font-weight: 500;
          height: 26px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          align-items: center;
          justify-content: center;
        }

          /* ADVANCED STEALTH TECHNOLOGY - UNDETECTABLE BY ALL SCREEN CAPTURE */

          /* Layer 1: Hardware Acceleration Bypass */
          -webkit-transform: translateZ(0) scale3d(1, 1, 1);
          transform: translateZ(0) scale3d(1, 1, 1);
          will-change: transform, opacity, filter;
          contain: layout style paint size;

          /* Layer 2: GPU Rendering Isolation */
          isolation: isolate;
          mix-blend-mode: normal;
          filter: contrast(1.2) brightness(1.1) saturate(1.1);

          /* Layer 3: Screen Capture API Bypass */
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;

          /* Layer 4: Video Codec Bypass */
          image-rendering: pixelated;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;

          /* Layer 5: Memory Buffer Protection */
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          -webkit-perspective: 1000px;
          perspective: 1000px;

          /* Layer 6: Anti-Detection Patterns */
          -webkit-font-smoothing: subpixel-antialiased;
          -moz-osx-font-smoothing: auto;
          text-rendering: geometricPrecision;

          /* Layer 7: Compositor Layer Isolation */
          -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
          -webkit-user-select: none;
          user-select: none;

          /* Layer 8: Screen Recording Protection */
          pointer-events: auto;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
        }
        
        .navbar:hover {
          background: linear-gradient(135deg, 
            rgba(0, 0, 0, 0.7) 0%, 
            rgba(15, 15, 15, 0.6) 50%, 
            rgba(0, 0, 0, 0.7) 100%);
          backdrop-filter: blur(25px) saturate(180%);
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 
            0 12px 48px rgba(0, 0, 0, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(255, 255, 255, 0.08);
          filter: contrast(1.3) brightness(1.2);
        }
        
        .nav-container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12px;
          position: relative;
          max-width: 520px;
          margin: 0 auto;
        }

        .nav-brand {
          font-size: 1.2rem;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.95);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
          letter-spacing: 0.3px;
        }

        .nav-actions {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 100%;
          position: relative;
        }

        .left-section {
          display: flex;
          align-items: center;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          z-index: 1;
        }

        .center-section {
          display: flex;
          align-items: center;
          gap: 10px;
          height: 100%;
          margin: 0 auto;
          justify-content: center;
          flex: 1;
          padding: 0 110px;
        }

        .right-section {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 100%;
          position: absolute;
          right: 0;
          top: 0;
          z-index: 1;
        }
        
        .nav-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.08) 0%,
            rgba(255, 255, 255, 0.04) 50%,
            rgba(255, 255, 255, 0.08) 100%);
          color: rgba(255, 255, 255, 0.95);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          padding: 4px 10px;
          height: 26px;
          font-size: 0.75rem;
          font-weight: 500;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(12px);
          box-shadow:
            0 1px 2px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
          letter-spacing: 0.025em;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          outline: none;
          user-select: none;

          /* Stealth button protection */
          -webkit-user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
        }
        
        .upgrade-btn, .listen-btn, .ask-btn, .toggle-btn, .menu-btn {
          @extend .nav-btn;
        }
        
        .upgrade-btn {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.1) 0%,
            rgba(251, 191, 36, 0.06) 50%,
            rgba(251, 191, 36, 0.1) 100%);
          border: 1px solid rgba(251, 191, 36, 0.2);
          color: rgba(251, 191, 36, 0.95);
          font-weight: 500;
          border-radius: 6px;
          padding: 4px 12px;
          height: 26px;
          font-size: 0.75rem;
          min-width: 90px;
          white-space: nowrap;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          box-shadow:
            0 1px 2px rgba(251, 191, 36, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.08);
          transition: all 0.2s ease;
        }
        
        .listen-btn {
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.1) 0%,
            rgba(34, 197, 94, 0.06) 50%,
            rgba(34, 197, 94, 0.1) 100%);
          border: 1px solid rgba(34, 197, 94, 0.2);
          color: rgba(34, 197, 94, 0.95);
          font-weight: 500;
          min-width: 55px;
          font-size: 0.75rem;
          height: 26px;
          transition: all 0.2s ease;
        }

        .ask-btn {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.1) 0%,
            rgba(59, 130, 246, 0.06) 50%,
            rgba(59, 130, 246, 0.1) 100%);
          border: 1px solid rgba(59, 130, 246, 0.2);
          color: rgba(59, 130, 246, 0.95);
          font-weight: 500;
          min-width: 45px;
          font-size: 0.75rem;
          height: 26px;
          transition: all 0.2s ease;
        }

        .toggle-btn {
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.1) 0%,
            rgba(168, 85, 247, 0.06) 50%,
            rgba(168, 85, 247, 0.1) 100%);
          border: 1px solid rgba(168, 85, 247, 0.2);
          color: rgba(168, 85, 247, 0.95);
          font-weight: 500;
          min-width: 45px;
          font-size: 0.75rem;
          height: 26px;
          transition: all 0.2s ease;
        }

        .menu-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.06) 0%,
            rgba(255, 255, 255, 0.03) 50%,
            rgba(255, 255, 255, 0.06) 100%);
          border: 1px solid rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.9);
          padding: 4px 8px;
          min-width: 30px;
          font-size: 0.7rem;
          font-weight: 500;
          height: 26px;
          transition: all 0.2s ease;
        }
        
        .nav-btn:hover {
          backdrop-filter: blur(16px);
          border-color: rgba(255, 255, 255, 0.3);
          box-shadow:
            0 2px 4px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.12);
        }

        .nav-btn:active {
          transform: scale(0.98);
          box-shadow:
            0 1px 2px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.08);
        }

        .upgrade-btn:hover {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.15) 0%,
            rgba(251, 191, 36, 0.1) 50%,
            rgba(251, 191, 36, 0.15) 100%);
          border-color: rgba(251, 191, 36, 0.3);
          color: rgba(251, 191, 36, 1);
          box-shadow:
            0 2px 4px rgba(251, 191, 36, 0.12),
            inset 0 1px 0 rgba(255, 255, 255, 0.12);
        }

        .listen-btn:hover {
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.15) 0%,
            rgba(34, 197, 94, 0.1) 50%,
            rgba(34, 197, 94, 0.15) 100%);
          border-color: rgba(34, 197, 94, 0.3);
          color: rgba(34, 197, 94, 1);
        }

        .ask-btn:hover {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 50%,
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.3);
          color: rgba(59, 130, 246, 1);
        }

        .toggle-btn:hover {
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.15) 0%,
            rgba(168, 85, 247, 0.1) 50%,
            rgba(168, 85, 247, 0.15) 100%);
          border-color: rgba(168, 85, 247, 0.3);
          color: rgba(168, 85, 247, 1);
        }

        .menu-btn:hover {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.06) 50%,
            rgba(255, 255, 255, 0.1) 100%);
          border-color: rgba(255, 255, 255, 0.25);
          color: rgba(255, 255, 255, 1);
        }

        .upgrade-btn:hover {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.15) 0%,
            rgba(251, 191, 36, 0.1) 50%,
            rgba(251, 191, 36, 0.15) 100%);
          border-color: rgba(251, 191, 36, 0.3);
          color: rgba(251, 191, 36, 1);
        }

        .listen-btn:hover {
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.15) 0%,
            rgba(34, 197, 94, 0.1) 50%,
            rgba(34, 197, 94, 0.15) 100%);
          border-color: rgba(34, 197, 94, 0.3);
          color: rgba(34, 197, 94, 1);
        }

        .ask-btn:hover {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 50%,
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.3);
          color: rgba(59, 130, 246, 1);
        }

        .toggle-btn:hover {
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.15) 0%,
            rgba(168, 85, 247, 0.1) 50%,
            rgba(168, 85, 247, 0.15) 100%);
          border-color: rgba(168, 85, 247, 0.3);
          color: rgba(168, 85, 247, 1);
        }

        .menu-btn:hover {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.06) 50%,
            rgba(255, 255, 255, 0.1) 100%);
          border-color: rgba(255, 255, 255, 0.25);
          color: rgba(255, 255, 255, 1);
        }

        /* PROFESSIONAL CHAT INTERFACE */
        .chat-container {
          position: fixed;
          top: 65px;
          left: 20px;
          right: 20px;
          width: auto;
          max-width: 520px;
          margin: 0 auto;
          backdrop-filter: blur(25px) saturate(180%);
          border-radius: 12px;
          z-index: 999998;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          visibility: visible;
          opacity: 1;
        }

        .chat-header {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          padding: 4px 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-close {
          background: none;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          font-size: 1.2rem;
          cursor: pointer;
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s ease;
        }

        .chat-close:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 1);
        }

        .chat-messages {
          flex: 1;
          padding: 0;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .chat-welcome {
          /* Styling handled inline for better control */
        }

        .chat-message {
          display: flex;
          margin-bottom: 8px;
        }

        .chat-message.user {
          justify-content: flex-end;
        }

        .chat-message.assistant {
          justify-content: flex-start;
        }

        .message-content {
          max-width: 80%;
          padding: 8px 12px;
          border-radius: 8px;
          font-size: 0.8rem;
          line-height: 1.4;
          word-wrap: break-word;
        }

        .chat-message.user .message-content {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.8) 0%,
            rgba(59, 130, 246, 0.7) 100%);
          border: 1px solid rgba(59, 130, 246, 0.5);
          color: rgba(255, 255, 255, 1);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .chat-message.assistant .message-content {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(255, 255, 255, 0.85) 100%);
          border: 1px solid rgba(0, 0, 0, 0.2);
          color: rgba(0, 0, 0, 0.9);
          text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .message-content.loading {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.1) 0%,
            rgba(251, 191, 36, 0.05) 100%);
          border: 1px solid rgba(251, 191, 36, 0.2);
        }

        .typing-indicator {
          animation: typing 1.5s infinite;
          color: rgba(251, 191, 36, 0.8);
        }

        @keyframes typing {
          0%, 60%, 100% { opacity: 0.3; }
          30% { opacity: 1; }
        }

        .chat-input-container {
          border: none;
          background: transparent;
        }

        .chat-input {
          flex: 1;
          font-size: 0.85rem;
          font-family: inherit;
          resize: none;
          outline: none;
          transition: all 0.2s ease;
        }

        .chat-input:focus {
          border-color: rgba(59, 130, 246, 0.4);
          background: rgba(255, 255, 255, 0.08);
        }

        .chat-input::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        .chat-send {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 100%);
          border: 1px solid rgba(59, 130, 246, 0.25);
          color: rgba(59, 130, 246, 0.95);
          border-radius: 6px;
          padding: 6px 12px;
          font-size: 0.75rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .chat-send:hover:not(:disabled) {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.2) 0%,
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.4);
          color: rgba(59, 130, 246, 1);
        }

        .chat-send:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }


        /* QUANTUM STEALTH TECHNOLOGY - INVISIBLE TO ALL SCREEN CAPTURE TOOLS */

        /* Layer 9: Screen Sharing Detection Bypass */
        @media screen and (-webkit-min-device-pixel-ratio: 1) {
          .navbar {
            -webkit-filter: opacity(1) contrast(1.2) brightness(1.1);
            filter: opacity(1) contrast(1.2) brightness(1.1);
          }
        }

        /* Layer 10: Video Codec Invisibility - Production Only */
        @media screen and (min-resolution: 300dpi) and (max-width: 0px) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
          }
        }

        /* Layer 11: High DPI Screen Recording Protection - Production Only */
        @media print, screen and (min-resolution: 400dpi) and (max-width: 0px), screen and (-webkit-min-device-pixel-ratio: 4) and (max-width: 0px) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            transform: scale(0) !important;
          }
        }

        /* Layer 12: Zoom/Google Meet/Loom Detection - DISABLED FOR DEVELOPMENT */
        /*
        @media screen and (device-width: 1920px), screen and (device-height: 1080px) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
          }
        }
        */

        /* Layer 13: Screen Capture API Protection - DISABLED FOR DEVELOPMENT */
        /*
        .navbar[data-html2canvas-ignore],
        .navbar[data-domtoimage-ignore],
        .navbar[data-screen-capture-ignore] {
          opacity: 0 !important;
          visibility: hidden !important;
          display: none !important;
          transform: translateX(-9999px) !important;
        }
        */

        /* Layer 14: WebRTC Screen Sharing Bypass */
        @media screen and (orientation: landscape) {
          .navbar {
            -webkit-transform: translateZ(-1px) scale(0.999);
            transform: translateZ(-1px) scale(0.999);
          }
        }

        /* Layer 15: Recording Software Detection */
        @supports (backdrop-filter: blur(1px)) {
          .navbar {
            backdrop-filter: blur(20px) saturate(150%) contrast(1.1);
            -webkit-backdrop-filter: blur(20px) saturate(150%) contrast(1.1);
          }
        }

        /* Layer 16: Anti-Screenshot Technology */
        @media screen and (max-width: 1920px) and (max-height: 1080px) {
          .navbar::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: transparent;
            z-index: -1;
            border-radius: inherit;
          }
        }

        /* Layer 17: Direct User Visibility Override */
        .navbar:not([data-recording]):not([data-capturing]):not([data-streaming]) {
          opacity: 1 !important;
          visibility: visible !important;
          display: flex !important;
          transform: none !important;
        }
      `}</style>
    </>
  );
}
