/**
 * Simple Configuration for Interview Assistant
 * Basic settings for OpenRouter API integration
 */

interface SimpleConfig {
  OPENROUTER_API_KEY: string;
  OPENROUTER_ENDPOINT: string;
  OPENROUTER_MODEL: string;
  NODE_ENV: string;
  APP_URL: string;
  IS_DEVELOPMENT: boolean;
  IS_PRODUCTION: boolean;
}

function getConfig(): SimpleConfig {
  return {
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || 'sk-or-v1-development-default-key',
    OPENROUTER_ENDPOINT: 'https://openrouter.ai/api/v1/chat/completions',
    OPENROUTER_MODEL: 'deepseek/deepseek-r1-0528-qwen3-8b:free',
    NODE_ENV: process.env.NODE_ENV || 'development',
    APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
    IS_PRODUCTION: process.env.NODE_ENV === 'production'
  };
}

export const config = getConfig(); 