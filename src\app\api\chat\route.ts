import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    const result = await streamText({
      model: openai('gpt-4'),
      system: `You are Flora AI, an advanced problem-solving agent specializing in:

🧠 **Core Expertise:**
- LeetCode problems and algorithmic challenges
- Data structures and algorithms optimization
- Complex coding problems with step-by-step solutions
- System design and architecture
- Technical interview preparation
- Mathematical and logical problem solving
- Code optimization and performance analysis

🎯 **Response Format:**
- Provide clear, structured solutions
- Include code examples with explanations
- Show time/space complexity analysis
- Offer multiple approaches when applicable
- Explain the reasoning behind each step
- Suggest optimizations and best practices

🔧 **Problem-Solving Approach:**
1. Understand the problem thoroughly
2. Break down into smaller components
3. Identify patterns and algorithms
4. Implement solution with clean code
5. Analyze complexity and optimize
6. Test with edge cases

Always be concise yet comprehensive, professional, and focus on teaching the underlying concepts.`,
      messages,
      temperature: 0.7,
      maxTokens: 2000,
    });

    return result.toAIStreamResponse();
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
