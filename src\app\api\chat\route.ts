export async function POST(req: Request) {
  try {
    const { problem } = await req.json();

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.SITE_URL || 'http://localhost:3000',
        'X-Title': 'Flora AI Agent'
      },
      body: JSON.stringify({
        model: 'qwen/qwen2.5-vl-72b-instruct:free',
        messages: [
          {
            role: 'system',
            content: `You are Flora AI, an advanced problem-solving agent. You are NOT a chatbot or assistant. You are a specialized AI agent that analyzes and solves complex problems.

CORE CAPABILITIES:
• Advanced algorithmic problem solving (LeetCode, competitive programming)
• Data structures and algorithms optimization
• System design and architecture analysis
• Code optimization and performance analysis
• Mathematical and logical problem solving
• Technical interview problem resolution

RESPONSE PROTOCOL:
• Analyze the problem systematically
• Provide step-by-step solution methodology
• Include optimized code implementations
• Show time/space complexity analysis
• Offer multiple solution approaches when applicable
• Focus on teaching core concepts and patterns

OUTPUT FORMAT:
• Direct, technical, and solution-focused
• No conversational elements or pleasantries
• Structured analysis with clear sections
• Code examples with detailed explanations
• Performance metrics and optimization insights

You solve problems with precision and expertise, not conversation.`
          },
          {
            role: 'user',
            content: `PROBLEM TO SOLVE: ${problem}`
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    // Return the streaming response
    return new Response(response.body, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked'
      }
    });

  } catch (error) {
    console.error('Flora AI Agent Error:', error);
    return new Response(
      JSON.stringify({ error: 'Agent processing failed' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
