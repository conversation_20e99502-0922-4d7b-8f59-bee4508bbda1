# 🚀 Flora AI Production Deployment Guide

## 📋 **PRE-DEPLOYMENT CHECKLIST**

### **✅ Environment Setup**
- [ ] Production server provisioned
- [ ] Domain name configured
- [ ] SSL certificate installed
- [ ] Environment variables configured
- [ ] Database setup completed
- [ ] Redis/cache configured
- [ ] CDN configured
- [ ] Monitoring setup

### **✅ Security Configuration**
- [ ] API keys secured
- [ ] Rate limiting enabled
- [ ] CORS configured properly
- [ ] Security headers enabled
- [ ] Input validation active
- [ ] Encryption keys rotated
- [ ] Firewall rules configured
- [ ] Backup strategy implemented

### **✅ Performance Optimization**
- [ ] Code minified and optimized
- [ ] Images compressed
- [ ] Caching strategies implemented
- [ ] CDN configured
- [ ] Database indexes optimized
- [ ] Connection pooling enabled
- [ ] Memory limits configured
- [ ] Auto-scaling rules set

---

## 🛠️ **PRODUCTION SETUP**

### **1. Environment Variables**
Create a `.env` file with these required variables:

```bash
# Core Settings
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# AI Configuration
OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key-here

# Security (CHANGE THESE!)
ENCRYPTION_KEY=your-32-character-encryption-key-here
SESSION_SECRET=your-32-character-session-secret-here

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Monitoring
LOG_LEVEL=info
```

### **2. Database Setup (Optional)**
For advanced features, set up PostgreSQL:

```sql
-- Create database
CREATE DATABASE flora_ai_production;

-- Create user
CREATE USER flora_ai WITH PASSWORD 'secure_password_here';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE flora_ai_production TO flora_ai;
```

### **3. Redis Setup (Optional)**
For caching and real-time features:

```bash
# Install Redis
sudo apt-get install redis-server

# Configure Redis
sudo nano /etc/redis/redis.conf

# Enable authentication
requirepass your_redis_password_here

# Restart Redis
sudo systemctl restart redis
```

---

## 🔧 **DEPLOYMENT METHODS**

### **Method 1: Vercel (Recommended for Quick Deploy)**

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod

# Set environment variables
vercel env add OPENROUTER_API_KEY production
vercel env add ENCRYPTION_KEY production
vercel env add SESSION_SECRET production
```

### **Method 2: Docker Deployment**

```dockerfile
# Dockerfile
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --omit=dev

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

Deploy with Docker:
```bash
# Build image
docker build -t flora-ai .

# Run container
docker run -d \
  --name flora-ai-prod \
  -p 3000:3000 \
  --env-file .env \
  flora-ai
```

### **Method 3: Traditional VPS Deployment**

```bash
# 1. Setup Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 2. Install PM2 for process management
sudo npm install -g pm2

# 3. Clone repository
git clone https://github.com/your-username/flora-ai.git
cd flora-ai

# 4. Install dependencies
npm ci --omit=dev

# 5. Build application
npm run build

# 6. Create ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'flora-ai',
    script: './node_modules/.bin/next',
    args: 'start',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    max_memory_restart: '1G'
  }]
}
EOF

# 7. Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

---

## 🔒 **SECURITY CONFIGURATION**

### **1. NGINX Reverse Proxy**
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### **2. Firewall Configuration**
```bash
# UFW Firewall setup
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Fail2ban for intrusion prevention
sudo apt-get install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

---

## 📊 **MONITORING SETUP**

### **1. Health Check Endpoint**
The application includes a health check at `/api/health`:

```bash
# Test health check
curl https://your-domain.com/api/health

# Expected response:
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "version": "1.0.0"
}
```

### **2. Uptime Monitoring**
Set up external monitoring with:
- **UptimeRobot** (free tier available)
- **Pingdom** (comprehensive monitoring)
- **StatusCake** (global monitoring)

### **3. Application Monitoring**
Configure application monitoring:

```bash
# Install and configure Datadog agent
DD_AGENT_MAJOR_VERSION=7 DD_API_KEY=your_api_key DD_SITE="datadoghq.com" bash -c "$(curl -L https://s3.amazonaws.com/dd-agent/scripts/install_script.sh)"

# Configure logs
echo "logs_enabled: true" >> /etc/datadog-agent/datadog.yaml
sudo systemctl restart datadog-agent
```

---

## 🚀 **PERFORMANCE OPTIMIZATION**

### **1. CDN Configuration**
Set up CloudFlare for global performance:

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  poweredByHeader: false,
  compress: true,
  images: {
    domains: ['your-domain.com'],
    formats: ['image/webp', 'image/avif']
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, must-revalidate'
          }
        ]
      }
    ]
  }
}

module.exports = nextConfig
```

### **2. Database Optimization**
```sql
-- PostgreSQL optimization
-- Enable query caching
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';

-- Optimize memory
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';

-- Restart PostgreSQL
SELECT pg_reload_conf();
```

---

## 🔄 **BACKUP STRATEGY**

### **1. Database Backups**
```bash
#!/bin/bash
# backup-database.sh

BACKUP_DIR="/var/backups/flora-ai"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="flora_ai_production"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
pg_dump $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 7 days
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://your-backup-bucket/
```

### **2. Application Backups**
```bash
#!/bin/bash
# backup-application.sh

APP_DIR="/var/www/flora-ai"
BACKUP_DIR="/var/backups/flora-ai"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup application files
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C $APP_DIR .

# Keep only last 3 days
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +3 -delete
```

### **3. Automated Backup Schedule**
```bash
# Add to crontab (crontab -e)
# Daily database backup at 2 AM
0 2 * * * /path/to/backup-database.sh

# Weekly application backup on Sunday at 3 AM
0 3 * * 0 /path/to/backup-application.sh
```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues & Solutions**

#### **1. High Memory Usage**
```bash
# Check memory usage
free -h
pm2 monit

# Solution: Increase memory limits
pm2 restart flora-ai --max-memory-restart 2G
```

#### **2. Slow Response Times**
```bash
# Check performance
pm2 logs flora-ai --lines 100

# Solution: Scale application
pm2 scale flora-ai +2
```

#### **3. Database Connection Issues**
```bash
# Check database status
sudo systemctl status postgresql

# Test connection
psql -h localhost -U flora_ai -d flora_ai_production -c "SELECT 1;"

# Solution: Restart database
sudo systemctl restart postgresql
```

#### **4. SSL Certificate Issues**
```bash
# Check certificate expiry
openssl x509 -in /path/to/certificate.crt -text -noout | grep "Not After"

# Renew Let's Encrypt certificate
sudo certbot renew
sudo systemctl reload nginx
```

---

## 📈 **SCALING STRATEGIES**

### **1. Horizontal Scaling**
```bash
# Add more PM2 instances
pm2 scale flora-ai +2

# Or set specific number
pm2 scale flora-ai 4
```

### **2. Load Balancer Setup**
```nginx
upstream flora_ai_backend {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    location / {
        proxy_pass http://flora_ai_backend;
        # ... other proxy settings
    }
}
```

### **3. Database Scaling**
```sql
-- Read replicas setup
-- On master database
CREATE USER replicator WITH REPLICATION PASSWORD 'secure_password';

-- Configure streaming replication
-- In postgresql.conf:
-- wal_level = replica
-- max_wal_senders = 3
-- max_replication_slots = 3
```

---

## 🔍 **HEALTH MONITORING**

### **1. System Metrics**
Monitor these key metrics:
- **CPU Usage**: Should stay below 80%
- **Memory Usage**: Should stay below 85%
- **Disk Usage**: Should stay below 90%
- **Network I/O**: Monitor for unusual patterns
- **Response Time**: Should be below 200ms

### **2. Application Metrics**
- **Request Rate**: Requests per second
- **Error Rate**: Should be below 0.1%
- **Success Rate**: Should be above 99.9%
- **AI Model Performance**: Response accuracy
- **Cache Hit Rate**: Should be above 80%

### **3. Alerting Rules**
Set up alerts for:
- High error rates (>1%)
- Slow response times (>500ms)
- High memory usage (>90%)
- Database connection failures
- SSL certificate expiry (30 days)

---

## 🎯 **PRODUCTION CHECKLIST**

### **Before Go-Live**
- [ ] Load testing completed (10x expected traffic)
- [ ] Security penetration testing passed
- [ ] Backup and recovery tested
- [ ] Monitoring and alerting configured
- [ ] SSL certificate installed and tested
- [ ] CDN configured and tested
- [ ] Rate limiting tested
- [ ] Error handling tested
- [ ] Documentation updated
- [ ] Team trained on operations

### **After Go-Live**
- [ ] Monitor application performance
- [ ] Check error rates hourly (first 24h)
- [ ] Verify backup systems
- [ ] Test alert notifications
- [ ] Monitor resource usage
- [ ] Check security logs
- [ ] Validate SSL certificate
- [ ] Test disaster recovery procedures
- [ ] Document any issues
- [ ] Plan capacity scaling

---

## 📞 **SUPPORT & MAINTENANCE**

### **Regular Maintenance Tasks**
- **Daily**: Check logs, monitor performance
- **Weekly**: Review security logs, update dependencies
- **Monthly**: Rotate encryption keys, review backups
- **Quarterly**: Security audit, performance review

### **Emergency Procedures**
1. **High Error Rate**: Scale horizontally, check logs
2. **Database Issues**: Switch to read replica, contact DBA
3. **Security Breach**: Block suspicious IPs, rotate keys
4. **Performance Issues**: Enable caching, scale resources

---

**🌸 Flora AI is now ready for production deployment with enterprise-grade reliability and performance! 🌸** 