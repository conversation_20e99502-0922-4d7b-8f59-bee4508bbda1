/**
 * Simple Error Handler for Interview Assistant
 * Basic error handling and logging
 */

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface SimpleError {
  type: ErrorType;
  message: string;
  timestamp: number;
}

export class SimpleErrorHandler {
  static handleError(error: any, context?: string): SimpleError {
    const timestamp = Date.now();
    let errorType = ErrorType.UNKNOWN_ERROR;
    let message = 'An unexpected error occurred';

    if (error instanceof Error) {
      message = error.message;
      
      // Determine error type based on message
      if (message.includes('fetch') || message.includes('network')) {
        errorType = ErrorType.NETWORK_ERROR;
      } else if (message.includes('API') || message.includes('400') || message.includes('500')) {
        errorType = ErrorType.API_ERROR;
      } else if (message.includes('validation') || message.includes('invalid')) {
        errorType = ErrorType.VALIDATION_ERROR;
      }
    }

    const simpleError: SimpleError = {
      type: errorType,
      message,
      timestamp
    };

    // Log error to console
    console.error(`[ERROR] ${context || 'Unknown'}: ${message}`, error);

    return simpleError;
  }

  static getErrorMessage(error: SimpleError): string {
    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
        return 'Network connection error. Please check your internet connection and try again.';
      case ErrorType.API_ERROR:
        return 'Service temporarily unavailable. Please try again in a moment.';
      case ErrorType.VALIDATION_ERROR:
        return 'Invalid input. Please check your input and try again.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }
}

export const errorHandler = SimpleErrorHandler; 